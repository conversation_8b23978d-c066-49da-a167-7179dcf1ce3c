#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试修复后的AppleScript功能
"""

import os
import subprocess
import tempfile
from datetime import datetime

def test_applescript_simple():
    """测试简化的AppleScript方法"""
    print("🧪 测试简化AppleScript方法")
    
    # 检查Excel是否安装
    try:
        result = subprocess.run(['osascript', '-e', 'tell application "Microsoft Excel" to get name'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ Microsoft Excel已安装")
        else:
            print("❌ Microsoft Excel未安装或无法访问")
            return False
    except Exception as e:
        print(f"❌ 检查Excel失败: {e}")
        return False
    
    # 检查pngpaste
    try:
        result = subprocess.run(['which', 'pngpaste'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ pngpaste工具可用")
        else:
            print("❌ pngpaste工具不可用")
            return False
    except Exception as e:
        print(f"❌ 检查pngpaste失败: {e}")
        return False
    
    # 检查Excel文件
    excel_path = "2015.xlsx"
    if not os.path.exists(excel_path):
        print(f"❌ Excel文件不存在: {excel_path}")
        return False
    
    print(f"✅ Excel文件存在: {excel_path}")
    
    # 测试AppleScript
    excel_abs_path = os.path.abspath(excel_path)
    
    # 简单的测试脚本
    test_script = f'''
tell application "Microsoft Excel"
    activate
    open workbook workbook file name "{excel_abs_path}"
    delay 1
    tell active workbook
        tell active sheet
            select range "A1:C5"
            copy range "A1:C5" as picture
        end tell
    end tell
    delay 2
    close active workbook saving no
end tell
'''
    
    print("🔄 执行AppleScript测试...")
    try:
        result = subprocess.run(['osascript', '-e', test_script], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode != 0:
            print(f"❌ AppleScript执行失败: {result.stderr}")
            return False
        
        print("✅ AppleScript执行成功")
        
        # 测试剪贴板
        temp_file = tempfile.mktemp(suffix='.png')
        paste_result = subprocess.run(['pngpaste', temp_file], 
                                    capture_output=True, timeout=10)
        
        if paste_result.returncode == 0 and os.path.exists(temp_file):
            print(f"✅ 成功从剪贴板获取图片: {temp_file}")
            
            # 移动到最终位置
            final_path = f"test_output_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            os.rename(temp_file, final_path)
            print(f"🎉 测试成功！图片保存为: {final_path}")
            
            # 打开图片预览
            subprocess.run(['open', final_path])
            return True
        else:
            print(f"❌ 从剪贴板获取图片失败: {paste_result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False

if __name__ == "__main__":
    print("🚀 快速测试开始")
    print("=" * 40)
    
    success = test_applescript_simple()
    
    if success:
        print("\n🎉 所有测试通过！AppleScript功能正常")
    else:
        print("\n❌ 测试失败，请检查错误信息")
