#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Mac截图功能测试脚本
"""

import os
import sys
from datetime import datetime
from excel_screenshot_hotkey import export_sheet_as_image, check_macos_dependencies, SYSTEM_OS

def test_mac_screenshot():
    """测试Mac截图功能"""
    print("🧪 Mac截图功能测试")
    print("=" * 50)
    
    # 检查系统
    print(f"🖥️ 当前系统: {SYSTEM_OS}")
    
    if SYSTEM_OS != "Darwin":
        print("⚠️ 此测试仅适用于macOS系统")
        return
    
    # 检查依赖
    print("\n🔍 检查依赖...")
    deps_ok = check_macos_dependencies()
    
    if not deps_ok:
        print("❌ 依赖检查失败，某些功能可能不可用")
    
    # 测试参数
    excel_path = input("\n📁 请输入Excel文件路径（或按回车使用默认）: ").strip()
    if not excel_path:
        excel_path = "2015.xlsx"  # 默认文件
    
    if not os.path.exists(excel_path):
        print(f"❌ 文件不存在: {excel_path}")
        print("💡 请确保Excel文件存在，或修改test_mac_screenshot.py中的默认路径")
        return
    
    sheet_name = input("📄 请输入工作表名称（或按回车使用默认）: ").strip()
    if not sheet_name:
        sheet_name = "2015年接待记录"  # 默认工作表
    
    area_str = input("📍 请输入区域（如A1:C5，或按回车使用默认）: ").strip()
    if not area_str:
        area_str = "A1:C5"  # 默认区域
    
    output_path = f"test_screenshot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
    
    print(f"\n📋 测试参数:")
    print(f"   文件: {excel_path}")
    print(f"   工作表: {sheet_name}")
    print(f"   区域: {area_str}")
    print(f"   输出: {output_path}")
    
    # 执行测试
    try:
        print(f"\n🚀 开始导出...")
        result_path = export_sheet_as_image(
            excel_path=excel_path,
            sheet_name=sheet_name,
            area_str=area_str,
            output_image_path=output_path,
            use_screenshot=True  # 启用截图功能
        )
        
        print(f"\n🎉 测试成功！")
        print(f"📸 图片已保存: {result_path}")
        
        # 打开图片预览
        if os.path.exists(result_path):
            import subprocess
            subprocess.run(['open', result_path])
            print("👀 图片已在预览中打开")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        print("\n🔧 故障排除建议:")
        print("1. 确保Excel文件存在且可访问")
        print("2. 确保工作表名称正确")
        print("3. 确保区域格式正确（如A1:C5）")
        print("4. 如果使用AppleScript，确保已授权相关权限")
        print("5. 尝试使用手动截图方案")

def main():
    """主函数"""
    try:
        test_mac_screenshot()
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
