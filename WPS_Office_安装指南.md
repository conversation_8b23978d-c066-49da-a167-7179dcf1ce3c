# WPS Office 安装和使用指南

## 🎯 为什么选择WPS Office？

✅ **免费使用** - 无需购买Microsoft Office
✅ **完美兼容** - 支持Excel文件格式
✅ **所见即所得** - 导出效果与Windows COM接口相同
✅ **安装简单** - 无需复杂配置
✅ **中文友好** - 金山软件出品，中文支持优秀

## 📦 安装WPS Office

### 方法1: 从App Store安装 (推荐)
1. 打开Mac App Store
2. 搜索 "WPS Office"
3. 点击"获取"进行安装
4. 安装完成后在应用程序中找到"WPS Office"

### 方法2: 从官网下载
1. 访问 WPS Office 官网: https://www.wps.cn/
2. 选择Mac版本下载
3. 下载完成后双击安装包
4. 按照提示完成安装

## 🔧 配置和测试

### 1. 首次启动WPS Office
- 启动后可能需要注册账号（可跳过）
- 允许访问文件和文件夹权限

### 2. 测试Excel文件打开
```bash
# 在终端中测试
open -a "WPS Office" /path/to/your/excel/file.xlsx
```

### 3. 验证AppleScript支持
```bash
# 测试AppleScript是否能控制WPS Office
osascript -e 'tell application "WPS Office" to get name'
```

## 🚀 使用Excel导出工具

### 1. 确保依赖已安装
```bash
# 安装Python依赖
pip install pillow openpyxl

# 安装pngpaste工具
brew install pngpaste
```

### 2. 运行导出工具
```bash
cd /Users/<USER>/Desktop/code
python3 excel_screenshot_hotkey.py
```

### 3. 预期输出
```
🖥️ 检测到操作系统: Darwin
🚀 Excel截图导出工具 - 支持WPS Office
==================================================
🔍 检查macOS依赖工具...
✅ 检测到Office应用: WPS Office
   路径: /Applications/WPS Office.app
✅ pngpaste工具可用
📖 正在处理Excel文件: 2015.xlsx
📄 工作表: 2015年接待记录
📍 区域: A1:C5
📊 使用WPS Office AppleScript导出...
✅ 图片已保存: 导出图片_20250608_XXXXXX.png
```

## ⚠️ 常见问题和解决方案

### 问题1: "WPS Office"未找到
**解决方案:**
- 确保WPS Office已正确安装
- 检查应用程序文件夹中是否有"WPS Office"
- 尝试重新安装WPS Office

### 问题2: AppleScript权限被拒绝
**解决方案:**
1. 打开"系统偏好设置" → "安全性与隐私"
2. 选择"隐私"标签
3. 在左侧选择"辅助功能"
4. 添加"终端"和"Python"
5. 在"自动化"中允许终端控制WPS Office

### 问题3: pngpaste不可用
**解决方案:**
```bash
# 安装Homebrew (如果未安装)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装pngpaste
brew install pngpaste
```

### 问题4: Excel文件无法打开
**解决方案:**
- 确保Excel文件路径正确
- 检查文件是否损坏
- 尝试手动用WPS Office打开文件

## 🎉 成功标志

当您看到以下输出时，说明WPS Office集成成功：

```
📊 使用WPS Office AppleScript导出...
✅ 图片已保存: 导出图片_XXXXXXXX_XXXXXX.png
🎉 导出成功！图片路径: 导出图片_XXXXXXXX_XXXXXX.png
```

## 💡 使用技巧

1. **批量处理**: 可以修改脚本中的文件路径进行批量导出
2. **自定义区域**: 支持任意Excel区域，如"A1:Z100"
3. **高质量输出**: 导出的PNG图片保持原始Excel格式
4. **自动预览**: 导出完成后会自动打开图片预览

## 🆚 与Microsoft Excel对比

| 特性 | WPS Office | Microsoft Excel |
|------|------------|-----------------|
| 价格 | 免费 | 付费订阅 |
| 安装 | 简单 | 复杂 |
| 配置 | 无需额外配置 | 需要xlwings |
| 导出效果 | 完美 | 完美 |
| 中文支持 | 优秀 | 良好 |

**结论: WPS Office是Mac用户的最佳选择！**
