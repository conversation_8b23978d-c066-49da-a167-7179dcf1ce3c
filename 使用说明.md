# Excel截图导出工具 - 支持WPS Office

## 🎯 功能说明

这个工具可以直接从Excel文件中导出指定区域为图片，类似Windows COM接口的效果。**现在支持WPS Office！**

### 支持的平台和方法

| 平台 | 导出方法 | 效果 | 依赖 |
|------|----------|------|------|
| **Windows** | COM接口 | ✅ 所见即所得 | pywin32 |
| **macOS** | WPS Office AppleScript | ✅ 所见即所得 | WPS Office + pngpaste |
| **macOS** | Microsoft Excel + xlwings | ✅ 所见即所得 | Excel + xlwings + pngpaste |
| **Linux** | openpyxl | ⚠️ 基本渲染 | openpyxl |

## 📦 安装依赖

### Windows
```bash
pip install pywin32 pillow openpyxl
```

### macOS (推荐WPS Office)
```bash
pip install pillow openpyxl
brew install pngpaste
```

然后安装以下任一Office应用：
- **WPS Office** (推荐): 从App Store或官网下载
- **Microsoft Excel**: 需要额外安装 `pip install xlwings`

### Linux
```bash
pip install openpyxl pillow
```

## 🚀 使用方法

### 1. 基本调用
```python
from excel_screenshot_hotkey import export_sheet_as_image

result = export_sheet_as_image(
    excel_path="your_file.xlsx",
    sheet_name="Sheet1",
    area_str="A1:C5",
    output_image_path="output.png"
)
```

### 2. 运行示例
```bash
python3 excel_screenshot_hotkey.py
```

### 3. 参数说明
- `excel_path`: Excel文件路径
- `sheet_name`: 工作表名称
- `area_str`: 区域字符串，支持格式：
  - "A1:C5" (标准格式)
  - "(A1,C5)" (括号格式)
  - "A1,C5" (逗号格式)
- `output_image_path`: 输出图片路径

## 🔧 工作原理

### Windows (COM接口)
1. 启动Excel应用程序
2. 打开指定工作簿
3. 选择目标区域
4. 使用`CopyPicture`方法复制为图片
5. 从剪贴板获取图片并保存

### macOS (WPS Office AppleScript) ⭐推荐
1. 使用AppleScript启动WPS Office
2. 打开指定工作簿
3. 选择目标区域
4. 使用`copy as picture`方法复制
5. 通过pngpaste从剪贴板获取图片

### macOS (Microsoft Excel + xlwings)
1. 使用xlwings启动Excel
2. 打开指定工作簿
3. 选择目标区域
4. 使用`copy_picture`方法复制
5. 通过pngpaste从剪贴板获取图片

### Linux/备用 (openpyxl)
1. 读取Excel文件数据
2. 解析单元格样式
3. 使用PIL重新绘制表格
4. 保存为PNG图片

## ⚠️ 注意事项

### macOS用户
- **推荐使用WPS Office**: 免费且支持良好
- 或者安装Microsoft Excel + xlwings
- 首次使用可能需要授权应用访问文件
- 确保安装了pngpaste工具

### 所有平台
- 确保Excel文件存在且可访问
- 工作表名称必须正确
- 区域格式必须有效
- 输出目录必须有写入权限

## 🎉 优势

1. **直接导出**: 不需要手动截图
2. **所见即所得**: 保持Excel原始显示效果
3. **跨平台**: 支持Windows、macOS、Linux
4. **支持WPS Office**: Mac用户的免费选择
5. **自动化**: 可以批量处理
6. **高质量**: 输出清晰的PNG图片

## 💡 WPS Office vs Microsoft Excel

| 特性 | WPS Office | Microsoft Excel |
|------|------------|-----------------|
| **价格** | ✅ 免费 | ❌ 付费 |
| **安装简单** | ✅ 简单 | ⚠️ 需要额外配置 |
| **导出效果** | ✅ 所见即所得 | ✅ 所见即所得 |
| **依赖** | 仅需pngpaste | 需要xlwings |
| **推荐度** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |

**推荐使用WPS Office，免费且效果优秀！**
