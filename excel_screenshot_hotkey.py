#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel表格区域导出为图片工具 - 跨平台版本
支持Windows、macOS、Linux系统，提供所见即所得的直接导出效果

🖥️ Windows: 使用COM接口直接导出（所见即所得）
🍎 macOS: 使用xlwings库直接导出（所见即所得）
🐧 Linux: 使用openpyxl渲染

依赖安装：
Windows: pip install pywin32 pillow openpyxl
macOS: pip install xlwings pillow openpyxl
Linux: pip install openpyxl pillow

使用方法：
export_sheet_as_image(excel_path, sheet_name, area_str, output_image_path)

参数说明：
- excel_path: Excel文件路径
- sheet_name: 工作表名称
- area_str: 区域字符串，如 "A1:C5" 或 "(A1,C5)"
- output_image_path: 输出图片路径
"""

import os
import time
import platform
import subprocess
import tempfile
from datetime import datetime
from PIL import Image, ImageDraw, ImageFont
import openpyxl
from openpyxl.styles import Font, PatternFill, Border
from openpyxl.utils import get_column_letter

# 检测操作系统
SYSTEM_OS = platform.system()
print(f"🖥️ 检测到操作系统: {SYSTEM_OS}")

# 平台特有的导入
if SYSTEM_OS == "Windows":
    try:
        from PIL import ImageGrab
        import win32com.client
        WINDOWS_COM_AVAILABLE = True
        print("✅ Windows COM接口可用")
    except ImportError:
        WINDOWS_COM_AVAILABLE = False
        print("⚠️ Windows COM接口不可用，将使用通用方法")
else:
    WINDOWS_COM_AVAILABLE = False

# macOS特有的导入
if SYSTEM_OS == "Darwin":
    try:
        import xlwings as xw
        XLWINGS_AVAILABLE = True
        print("✅ xlwings可用")
    except ImportError:
        XLWINGS_AVAILABLE = False
        print("⚠️ xlwings不可用，请安装: pip install xlwings")
else:
    XLWINGS_AVAILABLE = False


def get_system_fonts():
    """获取系统字体路径"""
    fonts = []

    if SYSTEM_OS == "Windows":
        font_paths = [
            "C:/Windows/Fonts/msyh.ttc",        # 微软雅黑
            "C:/Windows/Fonts/simhei.ttf",      # 黑体
            "C:/Windows/Fonts/simsun.ttc",      # 宋体
            "C:/Windows/Fonts/arial.ttf",       # Arial
        ]
    elif SYSTEM_OS == "Darwin":  # macOS
        font_paths = [
            "/System/Library/Fonts/PingFang.ttc",      # 苹方
            "/System/Library/Fonts/STHeiti Light.ttc", # 黑体
            "/System/Library/Fonts/Arial.ttf",         # Arial
            "/Library/Fonts/Arial.ttf",
        ]
    else:  # Linux
        font_paths = [
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
            "/usr/share/fonts/truetype/noto/NotoSansCJK-Regular.ttc",
            "/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.otf",
        ]

    for font_path in font_paths:
        if os.path.exists(font_path):
            fonts.append(font_path)

    return fonts


def get_font(size=12):
    """获取可用字体"""
    fonts = get_system_fonts()

    for font_path in fonts:
        try:
            return ImageFont.truetype(font_path, size)
        except (OSError, IOError):
            continue

    # 如果都失败，使用默认字体
    try:
        return ImageFont.load_default()
    except:
        return None


def export_with_xlwings(excel_path, sheet_name, area_str, output_image_path):
    """使用xlwings直接导出（仅macOS）"""
    try:
        print("🍎 使用xlwings直接导出...")

        # 标准化区域字符串
        excel_range = normalize_area_string(area_str)

        # 打开Excel应用
        app = xw.App(visible=False)

        try:
            # 打开工作簿
            wb = app.books.open(excel_path)

            # 选择工作表
            try:
                ws = wb.sheets[sheet_name]
            except:
                ws = wb.sheets[0]
                print(f"⚠️ 未找到工作表'{sheet_name}'，使用第一个工作表: {ws.name}")

            # 选择区域
            range_obj = ws.range(excel_range)

            # 导出为图片
            # xlwings可以直接将范围复制为图片
            range_obj.copy_picture()

            # 从剪贴板获取图片
            if SYSTEM_OS == "Darwin":
                # 在macOS上使用pngpaste获取剪贴板图片
                temp_file = tempfile.mktemp(suffix='.png')
                paste_result = subprocess.run(['pngpaste', temp_file],
                                            capture_output=True, timeout=10)

                if paste_result.returncode == 0 and os.path.exists(temp_file):
                    # 成功获取图片
                    image = Image.open(temp_file)

                    # 确保输出目录存在
                    output_dir = os.path.dirname(output_image_path)
                    if output_dir and not os.path.exists(output_dir):
                        os.makedirs(output_dir)

                    image.save(output_image_path, "PNG")
                    os.remove(temp_file)  # 清理临时文件
                    return output_image_path
                else:
                    raise Exception("无法从剪贴板获取图片")

        finally:
            # 关闭工作簿和应用
            wb.close()
            app.quit()

    except Exception as e:
        print(f"❌ xlwings导出失败: {e}")
        raise


def get_clipboard_image():
    """从剪贴板获取图片（仅Windows）"""
    if SYSTEM_OS == "Windows" and WINDOWS_COM_AVAILABLE:
        try:
            image = ImageGrab.grabclipboard()
            return image
        except Exception as e:
            print(f"❌ 从剪贴板获取图片失败: {e}")
    return None








def normalize_area_string(area_str):
    """
    标准化区域字符串为Excel格式
    :param area_str: 区域字符串，如 "A1:C5" 或 "(A1,C5)" 或 "A1,C5"
    :return: Excel标准格式字符串，如 "A1:C5"
    """
    # 清理字符串，移除括号和空格
    area_str = area_str.strip().replace('(', '').replace(')', '')

    if ':' in area_str:
        # 已经是Excel标准格式 "A1:C5"
        return area_str
    elif ',' in area_str:
        # 逗号分隔格式 "A1,C5" 转换为 "A1:C5"
        start_cell, end_cell = area_str.split(',')
        return f"{start_cell.strip()}:{end_cell.strip()}"
    else:
        raise ValueError(f"无法解析区域字符串: {area_str}")


def rgb_to_hex(rgb):
    """将RGB颜色转换为十六进制"""
    if rgb and hasattr(rgb, 'rgb'):
        return f"#{rgb.rgb:06x}" if rgb.rgb else "#FFFFFF"
    return "#FFFFFF"


def export_with_com(excel_path, sheet_name, area_str, output_image_path):
    """使用COM接口导出（仅Windows）"""
    excel_app = None
    workbook = None

    try:
        print("🖥️ 使用Windows COM接口...")

        # 启动Excel应用程序
        excel_app = win32com.client.Dispatch("Excel.Application")
        excel_app.Visible = False
        excel_app.DisplayAlerts = False

        # 打开工作簿
        workbook = excel_app.Workbooks.Open(os.path.abspath(excel_path))

        # 选择工作表
        try:
            worksheet = workbook.Worksheets(sheet_name)
        except:
            worksheet = workbook.Worksheets(1)
            print(f"⚠️ 未找到工作表'{sheet_name}'，使用第一个工作表: {worksheet.Name}")

        # 选择指定区域
        excel_range = normalize_area_string(area_str)
        range_obj = worksheet.Range(excel_range)

        # 使用CopyPicture方法复制为图片
        range_obj.CopyPicture(1, 2)  # xlScreen, xlBitmap

        print("📋 正在从剪贴板获取图片...")
        time.sleep(0.5)

        # 从剪贴板获取图片
        image = get_clipboard_image()

        if not image:
            raise Exception("无法从剪贴板获取图片")

        # 确保输出目录存在
        output_dir = os.path.dirname(output_image_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # 保存图片
        image.save(output_image_path, "PNG")
        return output_image_path

    finally:
        # 清理资源
        try:
            if workbook:
                workbook.Close(False)
            if excel_app:
                excel_app.Quit()
        except:
            pass


def export_with_openpyxl(excel_path, sheet_name, area_str, output_image_path):
    """使用openpyxl导出（跨平台）"""
    try:
        print("🔧 使用openpyxl跨平台方法...")

        # 打开工作簿
        workbook = openpyxl.load_workbook(excel_path, data_only=True)

        # 选择工作表
        if sheet_name in workbook.sheetnames:
            worksheet = workbook[sheet_name]
        else:
            worksheet = workbook.active
            print(f"⚠️ 未找到工作表'{sheet_name}'，使用活动工作表: {worksheet.title}")

        # 解析区域
        excel_range = normalize_area_string(area_str)
        cell_range = worksheet[excel_range]

        # 获取区域数据和样式
        if isinstance(cell_range, tuple):
            # 多行多列
            rows = cell_range
        else:
            # 单个单元格
            rows = ((cell_range,),)

        # 计算图片尺寸
        num_rows = len(rows)
        num_cols = len(rows[0]) if rows else 0

        if num_rows == 0 or num_cols == 0:
            raise ValueError("选择区域为空")

        # 设置基本参数
        base_font_size = 12
        cell_padding = 8
        min_cell_width = 80

        # 获取字体
        font = get_font(base_font_size)

        # 计算每列宽度
        col_widths = []
        for col_idx in range(num_cols):
            max_width = min_cell_width
            for row_idx in range(num_rows):
                cell = rows[row_idx][col_idx]
                text = str(cell.value) if cell.value is not None else ""

                # 计算文本宽度
                if font and text:
                    try:
                        bbox = ImageDraw.Draw(Image.new('RGB', (1, 1))).textbbox((0, 0), text, font=font)
                        text_width = bbox[2] - bbox[0] + cell_padding * 2
                    except:
                        text_width = len(text) * 8 + cell_padding * 2
                else:
                    text_width = len(text) * 8 + cell_padding * 2

                max_width = max(max_width, text_width)
            col_widths.append(max_width)

        # 计算总尺寸
        total_width = sum(col_widths)
        total_height = num_rows * (base_font_size + cell_padding * 2)

        # 创建图片
        image = Image.new('RGB', (total_width, total_height), 'white')
        draw = ImageDraw.Draw(image)

        # 绘制表格
        current_y = 0
        for row_idx, row in enumerate(rows):
            current_x = 0
            row_height = base_font_size + cell_padding * 2

            for col_idx, cell in enumerate(row):
                cell_width = col_widths[col_idx]

                # 获取单元格样式
                bg_color = 'white'
                text_color = 'black'

                if hasattr(cell, 'fill') and cell.fill and hasattr(cell.fill, 'start_color'):
                    if cell.fill.start_color.rgb and cell.fill.start_color.rgb != '00000000':
                        try:
                            rgb_val = cell.fill.start_color.rgb
                            if len(rgb_val) == 8:  # ARGB格式
                                rgb_val = rgb_val[2:]  # 去掉Alpha通道
                            bg_color = f"#{rgb_val}"
                        except:
                            bg_color = 'white'

                if hasattr(cell, 'font') and cell.font and hasattr(cell.font, 'color'):
                    if cell.font.color and cell.font.color.rgb and cell.font.color.rgb != '00000000':
                        try:
                            rgb_val = cell.font.color.rgb
                            if len(rgb_val) == 8:  # ARGB格式
                                rgb_val = rgb_val[2:]  # 去掉Alpha通道
                            text_color = f"#{rgb_val}"
                        except:
                            text_color = 'black'

                # 绘制背景
                draw.rectangle([current_x, current_y, current_x + cell_width, current_y + row_height],
                             fill=bg_color, outline='black', width=1)

                # 绘制文本
                text = str(cell.value) if cell.value is not None else ""
                if text:
                    text_x = current_x + cell_padding
                    text_y = current_y + cell_padding

                    try:
                        draw.text((text_x, text_y), text, fill=text_color, font=font)
                    except Exception as e:
                        print(f"⚠️ 绘制文本失败: {e}")
                        try:
                            draw.text((text_x, text_y), text, fill=text_color)
                        except:
                            pass

                current_x += cell_width
            current_y += row_height

        # 确保输出目录存在
        output_dir = os.path.dirname(output_image_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # 保存图片
        image.save(output_image_path, "PNG")
        return output_image_path

    except Exception as e:
        print(f"❌ openpyxl导出失败: {str(e)}")
        raise


def export_sheet_as_image(excel_path, sheet_name, area_str, output_image_path):
    """
    从Excel文件中导出指定区域为图片
    :param excel_path: Excel文件路径
    :param sheet_name: Sheet名称
    :param area_str: 所选区域，如 "A2:C8" 或 "(A2,C8)"
    :param output_image_path: 输出图片路径
    :return: 图片保存路径
    """
    try:
        print(f"📖 正在处理Excel文件: {excel_path}")
        print(f"📄 工作表: {sheet_name}")
        print(f"📍 区域: {area_str}")

        # 检查文件是否存在
        if not os.path.exists(excel_path):
            raise FileNotFoundError(f"Excel文件不存在: {excel_path}")

        # 根据操作系统选择方法
        if SYSTEM_OS == "Windows" and WINDOWS_COM_AVAILABLE:
            # Windows使用COM接口
            try:
                result = export_with_com(excel_path, sheet_name, area_str, output_image_path)
                print(f"✅ 图片已保存: {result}")
                return result
            except Exception as e:
                print(f"⚠️ COM接口失败，尝试openpyxl方法: {e}")

        elif SYSTEM_OS == "Darwin" and XLWINGS_AVAILABLE:
            # macOS使用xlwings直接导出
            try:
                result = export_with_xlwings(excel_path, sheet_name, area_str, output_image_path)
                print(f"✅ 图片已保存: {result}")
                return result
            except Exception as e:
                print(f"⚠️ xlwings导出失败，尝试openpyxl方法: {e}")

        # 使用openpyxl方法（跨平台备用）
        print("🔧 使用openpyxl渲染方法...")
        result = export_with_openpyxl(excel_path, sheet_name, area_str, output_image_path)
        print(f"✅ 图片已保存: {result}")
        return result

    except Exception as e:
        print(f"❌ 导出失败: {str(e)}")
        raise





def check_macos_dependencies():
    """检查macOS依赖工具"""
    if SYSTEM_OS != "Darwin":
        return True

    print("🔍 检查macOS依赖工具...")

    # 检查xlwings
    if not XLWINGS_AVAILABLE:
        print("⚠️ xlwings不可用")
        print("📦 安装方法: pip install xlwings")
        return False

    # 检查pngpaste（用于剪贴板操作）
    try:
        result = subprocess.run(['which', 'pngpaste'], capture_output=True, text=True)
        if result.returncode != 0:
            print("⚠️ 未找到pngpaste工具")
            print("📦 安装方法: brew install pngpaste")
            return False
        else:
            print("✅ 依赖工具检查完成")
    except Exception as e:
        print(f"⚠️ 检查依赖时出错: {e}")
        return False

    return True


def main():
    """示例用法"""
    print("🚀 Excel截图导出工具 - 直接导出版")
    print("=" * 50)

    # 检查依赖
    if SYSTEM_OS == "Darwin":
        deps_ok = check_macos_dependencies()
        if not deps_ok:
            print("❌ 依赖检查失败，某些功能可能不可用")

    # 示例调用
    excel_path = "2015.xlsx"  # 替换为实际的Excel文件路径
    sheet_name = "2015年接待记录"     # 替换为实际的工作表名称
    area_str = "A1:C5"        # 替换为实际的区域
    output_path = f"导出图片_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"

    try:
        result_path = export_sheet_as_image(excel_path, sheet_name, area_str, output_path)
        print(f"\n🎉 导出成功！图片路径: {result_path}")

        # 在macOS上打开图片预览
        if SYSTEM_OS == "Darwin" and os.path.exists(result_path):
            subprocess.run(['open', result_path])

    except Exception as e:
        print(f"\n❌ 导出失败: {e}")
        if SYSTEM_OS == "Darwin" and not XLWINGS_AVAILABLE:
            print("💡 提示: 请安装xlwings以获得最佳效果: pip install xlwings")


if __name__ == "__main__":
    main()
