#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel表格区域导出为图片工具 - 跨平台增强版本
支持Windows、macOS、Linux系统，提供所见即所得的截图效果

🖥️ Windows: 使用COM接口获取实际显示效果（所见即所得）
🍎 macOS: 提供三种方法
   1. AppleScript自动截图（推荐，所见即所得）
   2. 手动截图（备用方案，所见即所得）
   3. openpyxl渲染（快速但效果一般）
🐧 Linux: 支持多种截图工具 + openpyxl渲染

依赖安装：
Windows: pip install pywin32 pillow openpyxl
macOS: pip install openpyxl pillow && brew install pngpaste
Linux: pip install openpyxl pillow

使用方法：
export_sheet_as_image(excel_path, sheet_name, area_str, output_image_path, use_screenshot=True)

参数说明：
- excel_path: Excel文件路径
- sheet_name: 工作表名称
- area_str: 区域字符串，如 "A1:C5" 或 "(A1,C5)"
- output_image_path: 输出图片路径
- use_screenshot: 是否优先使用截图方法（Mac上推荐为True）
"""

import os
import time
import platform
import subprocess
import tempfile
from datetime import datetime
from PIL import Image, ImageDraw, ImageFont
import openpyxl
from openpyxl.styles import Font, PatternFill, Border
from openpyxl.utils import get_column_letter

# 检测操作系统
SYSTEM_OS = platform.system()
print(f"🖥️ 检测到操作系统: {SYSTEM_OS}")

# Windows特有的导入
if SYSTEM_OS == "Windows":
    try:
        from PIL import ImageGrab
        import win32com.client
        WINDOWS_COM_AVAILABLE = True
        print("✅ Windows COM接口可用")
    except ImportError:
        WINDOWS_COM_AVAILABLE = False
        print("⚠️ Windows COM接口不可用，将使用通用方法")
else:
    WINDOWS_COM_AVAILABLE = False


def get_system_fonts():
    """获取系统字体路径"""
    fonts = []

    if SYSTEM_OS == "Windows":
        font_paths = [
            "C:/Windows/Fonts/msyh.ttc",        # 微软雅黑
            "C:/Windows/Fonts/simhei.ttf",      # 黑体
            "C:/Windows/Fonts/simsun.ttc",      # 宋体
            "C:/Windows/Fonts/arial.ttf",       # Arial
        ]
    elif SYSTEM_OS == "Darwin":  # macOS
        font_paths = [
            "/System/Library/Fonts/PingFang.ttc",      # 苹方
            "/System/Library/Fonts/STHeiti Light.ttc", # 黑体
            "/System/Library/Fonts/Arial.ttf",         # Arial
            "/Library/Fonts/Arial.ttf",
        ]
    else:  # Linux
        font_paths = [
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
            "/usr/share/fonts/truetype/noto/NotoSansCJK-Regular.ttc",
            "/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.otf",
        ]

    for font_path in font_paths:
        if os.path.exists(font_path):
            fonts.append(font_path)

    return fonts


def get_font(size=12):
    """获取可用字体"""
    fonts = get_system_fonts()

    for font_path in fonts:
        try:
            return ImageFont.truetype(font_path, size)
        except (OSError, IOError):
            continue

    # 如果都失败，使用默认字体
    try:
        return ImageFont.load_default()
    except:
        return None


def get_clipboard_image():
    """从剪贴板获取图片（仅Windows）"""
    if SYSTEM_OS == "Windows" and WINDOWS_COM_AVAILABLE:
        try:
            image = ImageGrab.grabclipboard()
            return image
        except Exception as e:
            print(f"❌ 从剪贴板获取图片失败: {e}")
    return None


def export_with_applescript_simple(excel_path, sheet_name, area_str, output_image_path):
    """使用简化的AppleScript方法（仅macOS）"""
    try:
        print("🍎 使用简化AppleScript方法...")

        # 确保Excel文件路径是绝对路径
        excel_abs_path = os.path.abspath(excel_path)
        excel_range = normalize_area_string(area_str)

        print(f"📂 打开Excel文件: {excel_abs_path}")
        print(f"📄 工作表: {sheet_name}")
        print(f"📍 区域: {excel_range}")

        # 简化的AppleScript，分步执行
        scripts = [
            # 1. 打开Excel文件
            f'''
tell application "Microsoft Excel"
    activate
    open workbook workbook file name "{excel_abs_path}"
end tell
''',
            # 2. 选择区域并复制
            f'''
tell application "Microsoft Excel"
    tell active workbook
        tell active sheet
            select range "{excel_range}"
            copy range "{excel_range}" as picture
        end tell
    end tell
end tell
''',
            # 3. 关闭文件
            '''
tell application "Microsoft Excel"
    close active workbook saving no
end tell
'''
        ]

        # 逐步执行AppleScript
        for i, script in enumerate(scripts, 1):
            print(f"🔄 执行步骤 {i}/3...")
            result = subprocess.run(['osascript', '-e', script],
                                  capture_output=True, text=True, timeout=15)

            if result.returncode != 0:
                print(f"⚠️ 步骤 {i} 警告: {result.stderr}")
                # 不立即失败，继续尝试

            time.sleep(1)  # 等待操作完成

        # 等待剪贴板更新
        print("📋 等待剪贴板更新...")
        time.sleep(2)

        # 使用pngpaste获取剪贴板图片
        temp_file = tempfile.mktemp(suffix='.png')
        print("🖼️ 从剪贴板获取图片...")
        paste_result = subprocess.run(['pngpaste', temp_file],
                                    capture_output=True, timeout=10)

        if paste_result.returncode == 0 and os.path.exists(temp_file):
            # 成功获取图片
            image = Image.open(temp_file)

            # 确保输出目录存在
            output_dir = os.path.dirname(output_image_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)

            image.save(output_image_path, "PNG")
            os.remove(temp_file)  # 清理临时文件
            print(f"✅ 图片已保存: {output_image_path}")
            return output_image_path
        else:
            error_msg = paste_result.stderr.decode() if paste_result.stderr else "剪贴板中没有图片数据"
            raise Exception(f"获取剪贴板图片失败: {error_msg}")

    except Exception as e:
        print(f"❌ 简化AppleScript失败: {e}")
        raise


def export_with_applescript_screenshot(excel_path, sheet_name, area_str, output_image_path):
    """使用AppleScript控制Excel进行截图（仅macOS）"""
    try:
        print("🍎 使用AppleScript控制Excel截图...")

        # 确保Excel文件路径是绝对路径，并转换为POSIX路径
        excel_abs_path = os.path.abspath(excel_path)

        # 标准化区域字符串
        excel_range = normalize_area_string(area_str)

        # 将AppleScript写入临时文件，避免命令行参数中的特殊字符问题
        applescript_content = f'''
tell application "Microsoft Excel"
    activate

    -- 打开工作簿
    set workbook_ref to open workbook workbook file name "{excel_abs_path}"

    -- 选择工作表
    try
        set worksheet_ref to worksheet "{sheet_name}" of workbook_ref
    on error
        set worksheet_ref to worksheet 1 of workbook_ref
    end try

    -- 选择指定区域
    select range "{excel_range}" of worksheet_ref

    -- 等待一下确保选择完成
    delay 0.5

    -- 复制选择的区域为图片
    copy range "{excel_range}" of worksheet_ref as picture

    -- 等待复制完成
    delay 1

    -- 关闭工作簿（不保存）
    close workbook_ref saving no

end tell
'''

        # 创建临时AppleScript文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.scpt', delete=False, encoding='utf-8') as f:
            f.write(applescript_content)
            script_file = f.name

        try:
            # 执行AppleScript文件
            result = subprocess.run(['osascript', script_file],
                                  capture_output=True, text=True, timeout=30)

            if result.returncode != 0:
                raise Exception(f"AppleScript执行失败: {result.stderr}")

            # 等待剪贴板更新
            time.sleep(1.5)

            # 使用pngpaste获取剪贴板图片
            temp_file = tempfile.mktemp(suffix='.png')
            paste_result = subprocess.run(['pngpaste', temp_file],
                                        capture_output=True, timeout=10)

            if paste_result.returncode == 0 and os.path.exists(temp_file):
                # 成功获取图片，移动到目标位置
                image = Image.open(temp_file)

                # 确保输出目录存在
                output_dir = os.path.dirname(output_image_path)
                if output_dir and not os.path.exists(output_dir):
                    os.makedirs(output_dir)

                image.save(output_image_path, "PNG")
                os.remove(temp_file)  # 清理临时文件
                return output_image_path
            else:
                raise Exception(f"pngpaste失败: {paste_result.stderr.decode() if paste_result.stderr else '未知错误'}")

        finally:
            # 清理临时脚本文件
            if os.path.exists(script_file):
                os.remove(script_file)

    except Exception as e:
        print(f"❌ AppleScript截图失败: {e}")
        raise


def export_with_screenshot_fallback(excel_path, sheet_name, area_str, output_image_path):
    """使用屏幕截图作为备用方案（macOS/Linux）"""
    try:
        print("📸 使用智能截图方案...")
        print("=" * 50)
        print("📋 操作步骤:")
        print("1. 程序将自动打开Excel文件")
        print("2. 请手动选择指定区域")
        print("3. 按回车键开始截图")
        print("4. 在截图界面中框选Excel区域")
        print("=" * 50)
        print(f"📁 文件: {excel_path}")
        print(f"📄 工作表: {sheet_name}")
        print(f"📍 区域: {area_str}")

        # 自动打开Excel文件
        excel_abs_path = os.path.abspath(excel_path)
        if SYSTEM_OS == "Darwin":
            try:
                print("🔄 正在打开Excel文件...")
                subprocess.run(['open', excel_abs_path], timeout=10)
                time.sleep(2)  # 等待Excel打开
                print("✅ Excel文件已打开")
            except Exception as e:
                print(f"⚠️ 自动打开Excel失败: {e}")
                print("请手动打开Excel文件")

        print(f"\n💡 请在Excel中:")
        print(f"   1. 切换到工作表: {sheet_name}")
        print(f"   2. 选择区域: {area_str}")
        print(f"   3. 确保区域清晰可见")

        # 等待用户准备
        input("\n✅ 准备好后按回车键开始截图...")

        if SYSTEM_OS == "Darwin":  # macOS
            # 使用macOS的screencapture命令进行交互式截图
            print("📸 截图工具已启动，请框选Excel中的目标区域...")
            print("💡 提示: 按空格键可以拖动整个窗口，按Esc取消")

            result = subprocess.run(['screencapture', '-i', output_image_path],
                                  timeout=120)  # 增加超时时间

            if result.returncode == 0 and os.path.exists(output_image_path):
                # 检查图片是否有效
                try:
                    from PIL import Image
                    img = Image.open(output_image_path)
                    width, height = img.size
                    if width > 10 and height > 10:  # 确保不是空图片
                        print(f"✅ 截图成功! 尺寸: {width}x{height}")
                        return output_image_path
                    else:
                        raise Exception("截图尺寸过小，可能截图失败")
                except Exception as e:
                    raise Exception(f"截图文件无效: {e}")
            else:
                raise Exception("截图取消或失败")

        else:  # Linux或其他系统
            # 尝试使用gnome-screenshot或其他截图工具
            screenshot_tools = [
                ('gnome-screenshot', ['-a', '-f', output_image_path]),
                ('scrot', ['-s', output_image_path]),
                ('import', [output_image_path])  # ImageMagick
            ]

            for tool, args in screenshot_tools:
                try:
                    print(f"🔄 尝试使用 {tool}...")
                    result = subprocess.run([tool] + args, timeout=120)

                    if result.returncode == 0 and os.path.exists(output_image_path):
                        print(f"✅ 使用 {tool} 截图成功")
                        return output_image_path

                except FileNotFoundError:
                    print(f"⚠️ {tool} 不可用")
                    continue
                except Exception as e:
                    print(f"⚠️ {tool} 失败: {e}")
                    continue

            raise Exception("未找到可用的截图工具")

    except Exception as e:
        print(f"❌ 屏幕截图失败: {e}")
        raise


def normalize_area_string(area_str):
    """
    标准化区域字符串为Excel格式
    :param area_str: 区域字符串，如 "A1:C5" 或 "(A1,C5)" 或 "A1,C5"
    :return: Excel标准格式字符串，如 "A1:C5"
    """
    # 清理字符串，移除括号和空格
    area_str = area_str.strip().replace('(', '').replace(')', '')

    if ':' in area_str:
        # 已经是Excel标准格式 "A1:C5"
        return area_str
    elif ',' in area_str:
        # 逗号分隔格式 "A1,C5" 转换为 "A1:C5"
        start_cell, end_cell = area_str.split(',')
        return f"{start_cell.strip()}:{end_cell.strip()}"
    else:
        raise ValueError(f"无法解析区域字符串: {area_str}")


def rgb_to_hex(rgb):
    """将RGB颜色转换为十六进制"""
    if rgb and hasattr(rgb, 'rgb'):
        return f"#{rgb.rgb:06x}" if rgb.rgb else "#FFFFFF"
    return "#FFFFFF"


def export_with_com(excel_path, sheet_name, area_str, output_image_path):
    """使用COM接口导出（仅Windows）"""
    excel_app = None
    workbook = None

    try:
        print("🖥️ 使用Windows COM接口...")

        # 启动Excel应用程序
        excel_app = win32com.client.Dispatch("Excel.Application")
        excel_app.Visible = False
        excel_app.DisplayAlerts = False

        # 打开工作簿
        workbook = excel_app.Workbooks.Open(os.path.abspath(excel_path))

        # 选择工作表
        try:
            worksheet = workbook.Worksheets(sheet_name)
        except:
            worksheet = workbook.Worksheets(1)
            print(f"⚠️ 未找到工作表'{sheet_name}'，使用第一个工作表: {worksheet.Name}")

        # 选择指定区域
        excel_range = normalize_area_string(area_str)
        range_obj = worksheet.Range(excel_range)

        # 使用CopyPicture方法复制为图片
        range_obj.CopyPicture(1, 2)  # xlScreen, xlBitmap

        print("📋 正在从剪贴板获取图片...")
        time.sleep(0.5)

        # 从剪贴板获取图片
        image = get_clipboard_image()

        if not image:
            raise Exception("无法从剪贴板获取图片")

        # 确保输出目录存在
        output_dir = os.path.dirname(output_image_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # 保存图片
        image.save(output_image_path, "PNG")
        return output_image_path

    finally:
        # 清理资源
        try:
            if workbook:
                workbook.Close(False)
            if excel_app:
                excel_app.Quit()
        except:
            pass


def export_with_openpyxl(excel_path, sheet_name, area_str, output_image_path):
    """使用openpyxl导出（跨平台）"""
    try:
        print("🔧 使用openpyxl跨平台方法...")

        # 打开工作簿
        workbook = openpyxl.load_workbook(excel_path, data_only=True)

        # 选择工作表
        if sheet_name in workbook.sheetnames:
            worksheet = workbook[sheet_name]
        else:
            worksheet = workbook.active
            print(f"⚠️ 未找到工作表'{sheet_name}'，使用活动工作表: {worksheet.title}")

        # 解析区域
        excel_range = normalize_area_string(area_str)
        cell_range = worksheet[excel_range]

        # 获取区域数据和样式
        if isinstance(cell_range, tuple):
            # 多行多列
            rows = cell_range
        else:
            # 单个单元格
            rows = ((cell_range,),)

        # 计算图片尺寸
        num_rows = len(rows)
        num_cols = len(rows[0]) if rows else 0

        if num_rows == 0 or num_cols == 0:
            raise ValueError("选择区域为空")

        # 设置基本参数
        base_font_size = 12
        cell_padding = 8
        min_cell_width = 80

        # 获取字体
        font = get_font(base_font_size)

        # 计算每列宽度
        col_widths = []
        for col_idx in range(num_cols):
            max_width = min_cell_width
            for row_idx in range(num_rows):
                cell = rows[row_idx][col_idx]
                text = str(cell.value) if cell.value is not None else ""

                # 计算文本宽度
                if font and text:
                    try:
                        bbox = ImageDraw.Draw(Image.new('RGB', (1, 1))).textbbox((0, 0), text, font=font)
                        text_width = bbox[2] - bbox[0] + cell_padding * 2
                    except:
                        text_width = len(text) * 8 + cell_padding * 2
                else:
                    text_width = len(text) * 8 + cell_padding * 2

                max_width = max(max_width, text_width)
            col_widths.append(max_width)

        # 计算总尺寸
        total_width = sum(col_widths)
        total_height = num_rows * (base_font_size + cell_padding * 2)

        # 创建图片
        image = Image.new('RGB', (total_width, total_height), 'white')
        draw = ImageDraw.Draw(image)

        # 绘制表格
        current_y = 0
        for row_idx, row in enumerate(rows):
            current_x = 0
            row_height = base_font_size + cell_padding * 2

            for col_idx, cell in enumerate(row):
                cell_width = col_widths[col_idx]

                # 获取单元格样式
                bg_color = 'white'
                text_color = 'black'

                if hasattr(cell, 'fill') and cell.fill and hasattr(cell.fill, 'start_color'):
                    if cell.fill.start_color.rgb and cell.fill.start_color.rgb != '00000000':
                        try:
                            rgb_val = cell.fill.start_color.rgb
                            if len(rgb_val) == 8:  # ARGB格式
                                rgb_val = rgb_val[2:]  # 去掉Alpha通道
                            bg_color = f"#{rgb_val}"
                        except:
                            bg_color = 'white'

                if hasattr(cell, 'font') and cell.font and hasattr(cell.font, 'color'):
                    if cell.font.color and cell.font.color.rgb and cell.font.color.rgb != '00000000':
                        try:
                            rgb_val = cell.font.color.rgb
                            if len(rgb_val) == 8:  # ARGB格式
                                rgb_val = rgb_val[2:]  # 去掉Alpha通道
                            text_color = f"#{rgb_val}"
                        except:
                            text_color = 'black'

                # 绘制背景
                draw.rectangle([current_x, current_y, current_x + cell_width, current_y + row_height],
                             fill=bg_color, outline='black', width=1)

                # 绘制文本
                text = str(cell.value) if cell.value is not None else ""
                if text:
                    text_x = current_x + cell_padding
                    text_y = current_y + cell_padding

                    try:
                        draw.text((text_x, text_y), text, fill=text_color, font=font)
                    except Exception as e:
                        print(f"⚠️ 绘制文本失败: {e}")
                        try:
                            draw.text((text_x, text_y), text, fill=text_color)
                        except:
                            pass

                current_x += cell_width
            current_y += row_height

        # 确保输出目录存在
        output_dir = os.path.dirname(output_image_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # 保存图片
        image.save(output_image_path, "PNG")
        return output_image_path

    except Exception as e:
        print(f"❌ openpyxl导出失败: {str(e)}")
        raise


def export_sheet_as_image(excel_path, sheet_name, area_str, output_image_path, use_screenshot=True):
    """
    从Excel文件中导出指定区域为图片
    :param excel_path: Excel文件路径
    :param sheet_name: Sheet名称
    :param area_str: 所选区域，如 "A2:C8" 或 "(A2,C8)"
    :param output_image_path: 输出图片路径
    :param use_screenshot: 是否优先使用截图方法（Mac上推荐）
    :return: 图片保存路径
    """
    try:
        print(f"📖 正在处理Excel文件: {excel_path}")
        print(f"📄 工作表: {sheet_name}")
        print(f"📍 区域: {area_str}")

        # 检查文件是否存在
        if not os.path.exists(excel_path):
            raise FileNotFoundError(f"Excel文件不存在: {excel_path}")

        # 根据操作系统选择方法
        if SYSTEM_OS == "Windows" and WINDOWS_COM_AVAILABLE:
            # Windows优先使用COM接口
            try:
                result = export_with_com(excel_path, sheet_name, area_str, output_image_path)
                print(f"✅ 图片已保存: {result}")
                return result
            except Exception as e:
                print(f"⚠️ COM接口失败，尝试openpyxl方法: {e}")
                # 如果COM失败，回退到openpyxl

        elif SYSTEM_OS == "Darwin" and use_screenshot:
            # macOS优先使用截图方法
            print("🍎 Mac系统检测到，提供以下选项:")
            print("1. 智能截图（推荐，所见即所得，最稳定）")
            print("2. 简化AppleScript截图（自动化，需要权限）")
            print("3. 完整AppleScript截图（功能更全，需要权限）")
            print("4. openpyxl渲染（快速但效果一般）")

            choice = input("请选择方法 (1/2/3/4，默认1): ").strip() or "1"

            if choice == "1":
                try:
                    result = export_with_screenshot_fallback(excel_path, sheet_name, area_str, output_image_path)
                    print(f"✅ 图片已保存: {result}")
                    return result
                except Exception as e:
                    print(f"⚠️ 智能截图失败: {e}")
                    print("🔄 尝试AppleScript方案...")
                    choice = "2"

            if choice == "2":
                try:
                    result = export_with_applescript_simple(excel_path, sheet_name, area_str, output_image_path)
                    print(f"✅ 图片已保存: {result}")
                    return result
                except Exception as e:
                    print(f"⚠️ 简化AppleScript失败: {e}")
                    print("🔄 尝试完整AppleScript方案...")
                    choice = "3"

            if choice == "3":
                try:
                    result = export_with_applescript_screenshot(excel_path, sheet_name, area_str, output_image_path)
                    print(f"✅ 图片已保存: {result}")
                    return result
                except Exception as e:
                    print(f"⚠️ 完整AppleScript失败: {e}")
                    print("🔄 回退到openpyxl方法...")

        # 使用openpyxl方法（跨平台备用）
        print("🔧 使用openpyxl渲染方法...")
        result = export_with_openpyxl(excel_path, sheet_name, area_str, output_image_path)
        print(f"✅ 图片已保存: {result}")
        return result

    except Exception as e:
        print(f"❌ 导出失败: {str(e)}")
        raise





def check_macos_dependencies():
    """检查macOS依赖工具"""
    if SYSTEM_OS != "Darwin":
        return True

    print("🔍 检查macOS依赖工具...")

    # 检查pngpaste
    try:
        result = subprocess.run(['which', 'pngpaste'], capture_output=True, text=True)
        if result.returncode != 0:
            print("⚠️ 未找到pngpaste工具")
            print("📦 安装方法: brew install pngpaste")
            install = input("是否现在安装pngpaste? (y/n): ").strip().lower()
            if install == 'y':
                print("🔧 正在安装pngpaste...")
                install_result = subprocess.run(['brew', 'install', 'pngpaste'],
                                              capture_output=True, text=True)
                if install_result.returncode == 0:
                    print("✅ pngpaste安装成功")
                else:
                    print(f"❌ pngpaste安装失败: {install_result.stderr}")
                    return False
            else:
                print("⚠️ 没有pngpaste，AppleScript截图功能将不可用")
                return False
        else:
            print("✅ pngpaste工具已安装")
    except Exception as e:
        print(f"⚠️ 检查pngpaste时出错: {e}")
        return False

    return True


def main():
    """示例用法"""
    print("🚀 Excel截图导出工具")
    print("=" * 50)

    # 检查依赖
    if SYSTEM_OS == "Darwin":
        check_macos_dependencies()

    # 示例调用
    excel_path = "2015.xlsx"  # 替换为实际的Excel文件路径
    sheet_name = "2015年接待记录"     # 替换为实际的工作表名称
    area_str = "A1:C5"        # 替换为实际的区域
    output_path = f"导出图片_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"

    try:
        result_path = export_sheet_as_image(excel_path, sheet_name, area_str, output_path)
        print(f"\n🎉 导出成功！图片路径: {result_path}")

        # 在macOS上打开图片预览
        if SYSTEM_OS == "Darwin" and os.path.exists(result_path):
            subprocess.run(['open', result_path])

    except Exception as e:
        print(f"\n❌ 导出失败: {e}")


if __name__ == "__main__":
    main()
